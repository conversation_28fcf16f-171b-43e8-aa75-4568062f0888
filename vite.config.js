import {fileURLToPath, URL} from 'node:url'

import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {ElementPlusResolver} from 'unplugin-vue-components/resolvers'

// https://vite.dev/config/
export default defineConfig({
    server: {
        host: '0.0.0.0',
        proxy: {
            '/dev-api': {
                target: 'http://localhost:8000',  // 你的后端地址
                changeOrigin: true,               // 修改请求头中的 Origin
                rewrite: (p) => p.replace(/^\/dev-api/, '')
            }
        }
    },
    plugins: [
        vue(),
        // vueDevTools(),
        AutoImport({
            resolvers: [ElementPlusResolver()],
        }),
        Components({
            resolvers: [ElementPlusResolver()],
        }),
    ],
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url))
        },
    },
    build: {
        assetsDir: 'static' // 原本默认是 assets，改为 static
    }
})
