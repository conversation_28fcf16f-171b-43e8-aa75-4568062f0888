<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图例测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-legend {
            position: relative;
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .graph-legend {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 12px;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 120px;
            backdrop-filter: blur(4px);
        }
        .legend-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            padding: 2px 0;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 8px;
            user-select: none;
        }
        .legend-header:hover {
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 3px;
        }
        .legend-title {
            font-weight: 600;
            color: #333;
            font-size: 13px;
            flex: 1;
        }
        .legend-toggle {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: transform 0.2s ease;
        }
        .legend-toggle.collapsed {
            transform: rotate(-90deg);
        }
        .legend-content {
            transition: all 0.2s ease;
        }
        .graph-legend.collapsed {
            min-width: auto;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            transition: background-color 0.2s ease;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .legend-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        .legend-item:last-child {
            margin-bottom: 0;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }
        .legend-color.purple {
            background-color: purple;
        }
        .legend-color.orange {
            background-color: #E6A23C;
        }
        .legend-color.red {
            background-color: red;
        }
        .legend-color.blue {
            background-color: #26b7ff;
        }
        .legend-text {
            color: #666;
            font-size: 11px;
            line-height: 1.2;
        }
        .description {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图例功能测试</h1>
        <div class="description">
            <h3>功能说明：</h3>
            <ul>
                <li><strong>紫色</strong>：重点异常节点（前5个节点且 normal_ratio < 0.5）</li>
                <li><strong>橙色</strong>：重点正常节点（前5个节点且 normal_ratio >= 0.5）</li>
                <li><strong>红色</strong>：异常节点（非前5个节点且 normal_ratio < 0.5）</li>
                <li><strong>蓝色</strong>：正常节点（其他节点）</li>
            </ul>
            <p>点击图例标题可以折叠/展开图例内容。</p>
        </div>
        
        <div class="demo-legend">
            <!-- 图例 -->
            <div class="graph-legend" id="legend">
                <div class="legend-header" onclick="toggleLegend()">
                    <div class="legend-title">节点图例</div>
                    <div class="legend-toggle" id="toggle">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="legend-content" id="content">
                    <div class="legend-item">
                        <div class="legend-color purple"></div>
                        <span class="legend-text">重点异常节点</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color orange"></div>
                        <span class="legend-text">重点正常节点</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color red"></div>
                        <span class="legend-text">异常节点</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color blue"></div>
                        <span class="legend-text">正常节点</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let collapsed = false;
        
        function toggleLegend() {
            collapsed = !collapsed;
            const legend = document.getElementById('legend');
            const toggle = document.getElementById('toggle');
            const content = document.getElementById('content');
            
            if (collapsed) {
                legend.classList.add('collapsed');
                toggle.classList.add('collapsed');
                content.style.display = 'none';
            } else {
                legend.classList.remove('collapsed');
                toggle.classList.remove('collapsed');
                content.style.display = 'block';
            }
        }
    </script>
</body>
</html>
