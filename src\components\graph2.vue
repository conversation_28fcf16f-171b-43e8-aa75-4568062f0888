<script setup>
import * as echarts from 'echarts'
import { ref, onMounted, nextTick, defineProps, defineExpose, computed } from 'vue'

const props = defineProps({
  series: {
    type: Array,
    default: () => []
  },
  groupsPerRow: {
    type: [Number, String],
    default: 3,
    validator: value => Number(value) > 0
  }
})

const chartRef = ref(null)

// 布局配置（增加固定间距）
const layoutConfig = {
  margin: 50,
  minNodeSpacing: 2.5
}

// 排列节点（支持指定节点半径，返回节点坐标数组）
const arrangeNodesWithSize = (count, area, nodeRadius) => {
  // 单组时，节点均匀分布在椭圆上
  if (props.series.length === 1) {
    const centerX = area.x + area.width / 2
    const centerY = area.y + area.height / 2
    const rx = area.width / 2 * 0.85 // 椭圆横半轴
    const ry = area.height / 2 * 0.85 // 椭圆纵半轴
    const angleStep = (2 * Math.PI) / count
    return Array.from({ length: count }).map((_, i) => {
      const angle = i * angleStep
      return {
        x: centerX + rx * Math.cos(angle),
        y: centerY + ry * Math.sin(angle)
      }
    })
  }
  // 多组时，使用原有随机防重叠算法
  const positions = []
  for (let i = 0; i < count; i++) {
    const newPosition = findValidPosition(
      area,
      positions.map(p => ({ ...p, radius: nodeRadius })),
      nodeRadius
    )
    positions.push(newPosition)
  }
  return positions
}

// 检查所有节点是否有重叠
const hasCollision = (positions, nodeRadius) => {
  for (let i = 0; i < positions.length; i++) {
    for (let j = i + 1; j < positions.length; j++) {
      const dx = positions[i].x - positions[j].x
      const dy = positions[i].y - positions[j].y
      const distance = Math.sqrt(dx * dx + dy * dy)
      if (distance < nodeRadius * 2 * 1.05) { // 1.05为安全系数
        return true
      }
    }
  }
  return false
}

// 获取节点大小（递减尝试，保证不重叠）
const getNodeSize = (nodesCount, containerSize) => {
  const maxSize = 70
  const minSize = 2
  let symbolSize = maxSize
  let positions = []
  const area = containerSize
  while (symbolSize >= minSize) {
    const nodeRadius = symbolSize / 2
    positions = arrangeNodesWithSize(nodesCount, area, nodeRadius)
    if (!hasCollision(positions, nodeRadius)) {
      break
    }
    symbolSize -= 2
  }
  symbolSize = Math.max(minSize, Math.min(maxSize, symbolSize))
  return { symbolSize }
}

// 计算团伙布局区域（增加间距保护）
const calculateGroupArea = (groupIndex, totalGroups, containerWidth, containerHeight) => {
  // 单组时，布局区域为容器中心的80%椭圆区域
  if (totalGroups === 1) {
    const scale = 0.7 // 区域缩放比例
    const width = containerWidth * scale
    const height = containerHeight * scale
    return {
      x: (containerWidth - width) / 2,
      y: (containerHeight - height) / 2,
      width,
      height
    }
  }
  const cols = Math.min(Number(props.groupsPerRow), totalGroups)
  const rows = Math.ceil(totalGroups / cols)

  const availableWidth = containerWidth - (cols + 1) * layoutConfig.margin
  const availableHeight = containerHeight - (rows + 1) * layoutConfig.margin

  const areaWidth = availableWidth / cols
  const areaHeight = availableHeight / rows

  const col = groupIndex % cols
  const row = Math.floor(groupIndex / cols)

  return {
    x: layoutConfig.margin + col * (areaWidth + layoutConfig.margin),
    y: layoutConfig.margin + row * (areaHeight + layoutConfig.margin),
    width: areaWidth,
    height: areaHeight
  }
}

// 精确碰撞检测（基于半径）
const checkCollision = (point, existingPoints, nodeRadius) => {
  return existingPoints.some(existing => {
    const dx = point.x - existing.x
    const dy = point.y - existing.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    return distance < (nodeRadius + existing.radius) * 1.1
  })
}

// 边界保护（防止节点超出区域）
const ensureWithinBounds = (point, area, nodeRadius) => {
  return {
    x: Math.max(area.x + nodeRadius, Math.min(area.x + area.width - nodeRadius, point.x)),
    y: Math.max(area.y + nodeRadius, Math.min(area.y + area.height - nodeRadius, point.y))
  }
}

// 优化位置搜索算法
const findValidPosition = (area, existingPoints, nodeRadius, maxAttempts = 100) => {
  if (existingPoints.length === 0) {
    return {
      x: area.x + area.width / 2,
      y: area.y + area.height / 2
    }
  }

  for (let i = 0; i < maxAttempts; i++) {
    const refPoint = existingPoints[Math.floor(Math.random() * existingPoints.length)]
    const angle = Math.random() * Math.PI * 2
    const distance = nodeRadius * layoutConfig.minNodeSpacing * (1 + Math.random())

    const candidate = {
      x: refPoint.x + Math.cos(angle) * distance,
      y: refPoint.y + Math.sin(angle) * distance
    }

    const boundedCandidate = ensureWithinBounds(candidate, area, nodeRadius)

    if (!checkCollision(boundedCandidate, existingPoints, nodeRadius)) {
      return boundedCandidate
    }
  }

  const gridSize = 10
  for (let row = 0; row < gridSize; row++) {
    for (let col = 0; col < gridSize; col++) {
      const candidate = {
        x: area.x + (col / gridSize) * area.width,
        y: area.y + (row / gridSize) * area.height
      }

      if (!checkCollision(candidate, existingPoints, nodeRadius)) {
        return ensureWithinBounds(candidate, area, nodeRadius)
      }
    }
  }

  return {
    x: area.x + area.width / 2,
    y: area.y + area.height / 2
  }
}

// 修改 arrangeNodes 调用
const arrangeNodes = (nodes, area) => {
  const nodeRadius = getNodeSize(nodes.length, area).symbolSize / 2
  return arrangeNodesWithSize(nodes.length, area, nodeRadius)
}

// 合并数据
const mergedGraphData = computed(() => {
  let allNodes = []
  let allLinks = []

  if (!Array.isArray(props.series) || !chartRef.value) {
    return { nodes: [], links: [], categories: [] }
  }

  const containerWidth = chartRef.value.clientWidth
  const containerHeight = chartRef.value.clientHeight

  props.series.forEach((item, groupIndex) => {
    if (!item?.nodes || !item?.links) return

    const groupArea = calculateGroupArea(
        groupIndex,
        props.series.length,
        containerWidth,
        containerHeight
    )

    const positions = arrangeNodes(item.nodes, groupArea)
    const graphSettings = getNodeSize(item.nodes.length, groupArea)

    const nodes = item.nodes.map((node, index) => ({
      ...node,
      id: node.id,
      category: groupIndex,
      symbolSize: graphSettings.symbolSize,
      x: positions[index].x,
      y: positions[index].y,
      fixed: true
    }))

    allNodes = allNodes.concat(nodes)

    const links = item.links.map(link => ({
      ...link,
      source: link.source,
      target: link.target,
      category: groupIndex,
      lineStyle: {
        width: 2,
        opacity: 0.75,
        curveness: 0.2
      }
    }))

    allLinks = allLinks.concat(links)
  })

  return {
    nodes: allNodes,
    links: allLinks,
    categories: props.series.map((_, index) => ({
      name: `团伙${index + 1}`
    }))
  }
})

onMounted(() => {
  nextTick(() => {
    if (!chartRef.value) return

    const chart = echarts.init(chartRef.value)
    const { nodes, links, categories } = mergedGraphData.value

    // 创建初始选中状态（所有团伙默认显示）
    const initialSelected = {}
    categories.forEach(cat => {
      initialSelected[cat.name] = true
    })

    const option = {
      legend: {
        show: true,
        data: categories.map(cat => cat.name),
        textStyle: { color: '#ECF7FF', fontSize: 16 },
        top: 20,
        right: 30,
        orient: 'vertical',
        itemGap: 20,
        selectedMode: 'multiple',
        selected: initialSelected
      },
      color: ['#26b7ff', '#21bcdb', '#2e99e5', '#2e5ce5', '#5c67e5', '#39bfb4', '#6159b2', '#7a5c99', '#995c8f'],
      series: [{
        type: 'graph',
        layout: 'none',
        roam: true,
        zoom: 1,
        center: ['50%', '50%'],
        categories,
        data: nodes,
        links: links,
        label: {
          show: true,
          color: '#fff',
          fontSize: 14,
          distance: 5,
          formatter: '{b}',
          emphasis: { fontSize: 16 }
        },
        lineStyle: {
          color: 'source',
          opacity: 0.8,
          width: 2
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: { width: 4 },
          label: { fontSize: 18 }
        },
        legendHoverLink: false // 关闭默认悬停行为
      }]
    }

    chart.setOption(option)

    // ==== 新增：手动处理图例悬停事件 ====
    // 构建分类组到节点索引的映射
    const categoryNodeIndices = {};
    nodes.forEach((node, index) => {
      const categoryIndex = node.category;
      if (!categoryNodeIndices[categoryIndex]) {
        categoryNodeIndices[categoryIndex] = [];
      }
      categoryNodeIndices[categoryIndex].push(index);
    });

    // 处理图例悬停事件
    chart.on('legendover', (params) => {
      const categoryName = params.name;
      const categoryIndex = categories.findIndex(cat => cat.name === categoryName);

      if (categoryIndex !== -1 && categoryNodeIndices[categoryIndex]) {
        // 高亮当前分类组的所有节点
        chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: categoryNodeIndices[categoryIndex]
        });
      }
    });

    // 处理图例悬停离开事件
    chart.on('legendout', () => {
      // 取消所有高亮
      chart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0
      });
    });
    // ==== 新增代码结束 ====

    // 添加图例选择事件监听（多选模式）
    chart.on('legendselectchanged', (params) => {
      const selected = params.selected
      const newNodes = nodes.map(node => {
        const groupName = categories[node.category].name
        const isSelected = selected[groupName] !== false

        return {
          ...node,
          itemStyle: {
            ...node.itemStyle,
            opacity: isSelected ? 0.95 : 0.2
          },
          label: {
            show: isSelected
          }
        }
      })

      const newLinks = links.map(link => {
        const groupName = categories[link.category].name
        const isSelected = selected[groupName] !== false

        return {
          ...link,
          lineStyle: {
            ...link.lineStyle,
            opacity: isSelected ? 0.75 : 0.1
          }
        }
      })

      chart.setOption({
        series: [{
          data: newNodes,
          links: newLinks
        }]
      })
    })

    const handleResize = () => {
      chart.resize()
      const newData = mergedGraphData.value
      chart.setOption({
        series: [{
          data: newData.nodes,
          links: newData.links
        }]
      })
    }

    window.addEventListener('resize', handleResize)
  })
})

// 导出图片
const exportImg = () => {
  const chart = echarts.getInstanceByDom(chartRef.value)
  if (chart) {
    chart.setOption({
      legend: { show: false }
    })

    const chartDataURL = chart.getDataURL({
      type: 'png',
      backgroundColor: '#fff',
      pixelRatio: window.devicePixelRatio * 2
    })

    chart.setOption({
      legend: { show: true }
    })

    const link = document.createElement('a')
    link.href = chartDataURL
    link.download = `研判结果_${+new Date()}.png`
    link.click()
  }
}

defineExpose({ exportImg })
</script>

<template>
  <div ref="chartRef" style="width:100%; height:100%; min-height:600px"></div>
</template>