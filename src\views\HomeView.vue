<script setup>
import ImportDialog from "@/components/ImportDialog.vue";
import ResultDialog from "@/components/ResultDialog.vue";
import SecondGraph from "@/components/SecondGraph.vue";
// 视频播放3秒后跳转到HomeView页面
import { onMounted, useTemplateRef, ref } from 'vue';
import { useRoute } from 'vue-router';
import { getProgress } from "@/api/index.js";
import { useUploadStore } from "@/stores/index.js";

const route = useRoute();
const uploadStore = useUploadStore();

const importDialogRef = useTemplateRef('importDialog');
const resultDialogRef = useTemplateRef('resultDialog');
const secondGraphRef = useTemplateRef('secondGraph');
const minConnections = ref(10);

onMounted(() => {
  if (route.query.result) {
    // 如果有结果参数，直接打开结果对话框
    resultDialogRef.value.open();
    // 调用 getProgress 获取进度和结果
    fetchProgressAndResult();
    return
  }
  setTimeout(() => {
    // router.push({ name: 'home' });
    importDialogRef.value.open()
  }, route.query.notDelay ? 0 : 3000); // 3秒后跳转
});

// 获取进度和结果的函数
const fetchProgressAndResult = async () => {
  try {
    const { code, data } = await getProgress({ session_id: route.query.session_id, min_connections: minConnections.value });  // 关系线的数量, (节点大于这个数的关系线的才显示),最大值给1，最小值15-20
    console.log('获取进度和结果成功:', data);
    resultDialogRef.value.updateData(data.data)
  } catch (error) {
    console.error('获取进度和结果出错:', error);
  }
};

// 处理节点选择事件
const handleNodeSelected = ({nodeRelationData, node}) => {
  secondGraphRef.value.updateData(nodeRelationData);
  secondGraphRef.value.open(node);
};
const handleDepthChange = ({node, depth}) => {
  resultDialogRef.value.getNodeRelationData(node, depth)
};
</script>

<template>
  <div class="welcome-view flex-column">
    <!-- <video class="welcome-video" src="@/assets/videos/welcome.mp4" autoplay muted loop preload="auto" /> -->


    <ImportDialog ref="importDialog" />
    <ResultDialog ref="resultDialog" v-model:minConnections="minConnections" @nodeSelected="handleNodeSelected" @refresh="fetchProgressAndResult" />
    <SecondGraph ref="secondGraph" @depthChange="handleDepthChange" />
  </div>
</template>

<style scoped lang="scss">
.welcome-view {
  flex: 1;
  overflow: hidden;
  background: url("@/assets/images/bg.png") no-repeat center;
  background-size: cover;
}
.welcome-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  flex: 1;
}
</style>