<template>
  <Teleport to="body">
    <Transition name="loading-fade" appear>
      <div v-if="visible" class="custom-loading-overlay">
        <div class="custom-loading-container">
          <img src="@/assets/images/loading.gif" alt="loading" class="loading-icon" />
          <p class="loading-text">{{ text }}</p>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref } from 'vue'

const visible = ref(false)
const text = ref('请稍候...')

// 显示loading
const show = (loadingText = '请稍候...') => {
  text.value = loadingText
  visible.value = true
}

// 隐藏loading
const hide = () => {
  visible.value = false
}

// 暴露方法
defineExpose({
  show,
  hide
})
</script>

<style lang="scss" scoped>
.custom-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.custom-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 30px;
  min-width: 200px;
}

.loading-icon {
  width: 320px;
  height: 320px;
  margin-bottom: 15px;
}

.loading-text {
  color: #fff;
  font-size: 24px;
  margin: 0;
  text-align: center;
}

// 渐隐渐显动画
.loading-fade-enter-active,
.loading-fade-leave-active {
  transition: opacity 0.3s ease;
}

.loading-fade-enter-from {
  opacity: 0;
}

.loading-fade-leave-to {
  opacity: 0;
}

.loading-fade-enter-to,
.loading-fade-leave-from {
  opacity: 1;
}
</style> 