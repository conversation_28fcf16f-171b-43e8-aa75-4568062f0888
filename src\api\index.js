import request from "@/utils/request.js";

export function startClean(params) {
    return request({
        url: '/api/v1/file/start_cleaning',
        method: 'get',
        params
    });
}
// /api/v1/file/state 获取进度
export function getProgress(params) {
    return request({
        url: '/api/v1/file/progress',
        method: 'get',
        params
    });
}

export function downloadResult(params) {
    return request({
        url: '/api/v1/file/download',
        method: 'get',
        params,
        responseType: 'blob'
    });
}
// api/v1/file/download_model
export function downloadTemplate(params) {
    return request({
        url: '/api/v1/file/download_model',
        method: 'get',
        params,
        responseType: 'blob'
    });
}
// 获取子节点关系
export function getNodeRelation(params) {
    return request({
        url: '/api/v1/file/filter',
        method: 'get',
        params
    });
}