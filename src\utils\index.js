
export function formatNumberWithCommas(num) {
    // 处理非数字输入
    if (num === null || num === undefined) return '0';

    // 转换为字符串并清理非数字字符（保留负号和小数点）
    const str = String(num).replace(/[^0-9.-]/g, '');

    // 分离整数和小数部分
    const parts = str.split('.');
    let integerPart = parts[0];
    const decimalPart = parts.length > 1 ? `.${parts[1]}` : '';

    // 处理负数
    const sign = integerPart.startsWith('-') ? '-' : '';
    integerPart = integerPart.replace('-', '');

    // 添加千分位逗号
    integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return sign + integerPart + decimalPart;
}


export function downloadBlob(blobData, extension = 'zip', filename = '清洗结果') {
    // 创建一个临时的链接元素
    const link = document.createElement('a');

    // 创建一个Blob对象
    const blob = new Blob([blobData], { type: 'application/octet-stream' });

    // 创建一个URL对象
    const url = URL.createObjectURL(blob);

    // 设置链接的href属性为Blob URL
    link.href = url;
    // 下载名称取response 表头里的content-disposition字段
    // 设置下载文件的名称
    link.download = `${filename}_${+new Date()}.${extension}`;

    // 触发点击事件下载文件
    document.body.appendChild(link);
    link.click();

    // 清理临时链接和URL对象
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

}