<script setup>
import * as echarts from 'echarts'
import 'echarts-gl'
import { ref, onMounted, nextTick, defineProps, defineExpose, computed } from 'vue'

const props = defineProps({
  series: {
    type: Array,
    default: () => []
  }
})

const chartRef = ref(null)
const minHeight = 0 // 设置为0表示无最小高度限制
// 可配置每行显示数量
const itemsPerRow = computed(() => {
  return props.series.length > 4 ? 4 : props.series.length
})

const getGraphSome = (nodesCount) => {
  // 点大小范围
  const MIN_SYMBOL    = 10;
  const MAX_SYMBOL    = 50;
  // 互斥力范围
  const MIN_REPULSION = 30;
  const MAX_REPULSION = 100;

  // 线条长度范围
  const MIN_EDGE_LEN  = 10;
  const MAX_EDGE_LEN  = 80;

  // 假设节点数在 1～100 之间；超过则按边界处理
  const n = Math.max(1, Math.min(nodesCount, 100));

  // 归一化系数
  const t = (n - 1) / (100 - 1);

  // 从 max → min 线性插值
  const lerp = (min, max, t) => max - (max - min) * t;

  return {
    symbolSize: lerp(MIN_SYMBOL,    MAX_SYMBOL,    t),
    force: {
      // initLayout: 'circular',
      repulsion:  lerp(MIN_REPULSION, MAX_REPULSION, t),
      edgeLength: lerp(MIN_EDGE_LEN,  MAX_EDGE_LEN,  t),
      gravity: 0.15,
    },
  };
};

// 合并所有分组为一个series，节点用category区分
const mergedGraphData = computed(() => {
  const colorList = ['#26b7ff', '#21bcdb', '#2e99e5', '#2e5ce5', '#5c67e5', '#39bfb4', '#6159b2', '#7a5c99', '#995c8f']
  let allNodes = []
  let allLinks = []
  let categories = []
  let nodeOffset = 0
  let nodeIdMap = {} // 用于映射原始索引到唯一id
  props.series.forEach((item, groupIdx) => {
    const symbolSize = getGraphSome(item.nodes.length).symbolSize
    categories.push({ name: `团伙${groupIdx+1}` })
    const nodes = item.nodes.map((node, idx) => {
      const uniqueId = `g${groupIdx}_${idx}`
      nodeIdMap[`${groupIdx}_${idx}`] = uniqueId
      return {
        ...node,
        id: uniqueId,
        name: node.name ? `g${groupIdx}_${node.name}` : uniqueId,
        symbolSize,
        category: groupIdx,
        itemStyle: {
          color: colorList[groupIdx % colorList.length],
          borderColor: '#fff',
          borderWidth: 2,
          opacity: 1
        }
      }
    })
    // links的source/target要用唯一id
    const links = item.links.map(link => {
      // 支持数字索引和字符串name
      const getId = v => typeof v === 'number' ? `g${groupIdx}_${v}` : `g${groupIdx}_${v}`
      return {
        ...link,
        source: getId(link.source),
        target: getId(link.target),
        category: groupIdx
      }
    })
    allNodes = allNodes.concat(nodes)
    allLinks = allLinks.concat(links)
    nodeOffset += item.nodes.length
  })
  return {
    nodes: allNodes,
    links: allLinks,
    categories
  }
})

const calculateLayout = () => {
  const margin = 5 // 边距百分比
  const chartWidth = (100 - margin * (itemsPerRow.value + 1)) / itemsPerRow.value
  const rowCount = Math.ceil(mergedGraphData.value.nodes.length / itemsPerRow.value)
  const containerHeight = 100 // 容器高度百分比

  // 计算基础高度（无最小限制时）
  let chartHeight = (containerHeight - margin * (rowCount + 1)) / rowCount

  // 如果有最小高度限制且计算结果小于最小值
  if (minHeight > 0 && chartHeight < minHeight) {
    chartHeight = minHeight
  }

  return mergedGraphData.value.nodes.map((_, index) => {
    const row = Math.floor(index / itemsPerRow.value)
    const col = index % itemsPerRow.value
    return {
      left: `${margin + col * (chartWidth + margin)}%`,
      top: `${margin + row * (chartHeight + margin)}%`,
      width: `${chartWidth}%`,
      height: `${chartHeight}%`
    }
  })
}

onMounted(() => {
  nextTick(() => {
    const chart = echarts.init(chartRef.value, null, {
      renderer: 'canvas',
      useDirtyRect: true
    })
    const { nodes, links, categories } = mergedGraphData.value
    const option = {
      legend: {
        show: true,
        textStyle: { color: '#ECF7FF', fontSize: 16 },
        top: 20,
        right: 30,
        orient: 'vertical',
        itemGap: 20,
        icon: 'rect'
      },
      color: ['#26b7ff', '#21bcdb', '#2e99e5', '#2e5ce5', '#5c67e5', '#39bfb4', '#6159b2', '#7a5c99', '#995c8f'],
      series: [{
        name: '关系图',
        type: 'graphGL',
        layout: 'forceAtlas2',
        modularity: true,
        data: nodes,
        links: links,
        categories: categories,
        focusNodeAdjacency: true,
        roam: true,
        label: {
          show: true,
          position: 'right',
          fontSize: 12,
          color: '#fff'
        },
        lineStyle: {
          color: 'source',
          curveness: 0.3,
          opacity: 0.8,
          width: 2
        },
        forceAtlas2: {
          steps: 1,
          stopThreshold: 0.1,
          jitterTolerence: 10,
          edgeWeight: [0.2, 1],
          gravity: 0.1,
          scaling: 1.5,
          preventOverlap: true
        }
      }]
    }
    chart.setOption(option)
    window.addEventListener('resize', () => chart.resize())
  })
})

const exportImg = () => {
  const chart = echarts.getInstanceByDom(chartRef.value)
  if (chart) {
    chart.setOption({
      legend: {
        show: false
      }
    })
    const chartDataURL = chart.getDataURL({
      type: 'png',
      backgroundColor: '#fff',
      pixelRatio: 2 // 提高导出图片质量
    })
    chart.setOption({
      legend: {
        show: true
      }
    })

    const link = document.createElement('a')
    link.href = chartDataURL
    link.download = `研判结果_WebGL_${+new Date()}.png`
    link.click()
  }
}

// 获取图表实例的方法
const getChartInstance = () => {
  return echarts.getInstanceByDom(chartRef.value)
}

// 更新数据的方法
const updateData = (newSeries) => {
  const chart = getChartInstance()
  if (chart) {
    const colorList = ['#26b7ff', '#21bcdb', '#2e99e5', '#2e5ce5', '#5c67e5', '#39bfb4', '#6159b2', '#7a5c99', '#995c8f']
    let allNodes = []
    let allLinks = []
    let categories = []
    let nodeOffset = 0
    let nodeIdMap = {}
    newSeries.forEach((item, groupIdx) => {
      const symbolSize = getGraphSome(item.nodes.length).symbolSize
      categories.push({ name: `团伙${groupIdx+1}` })
      const nodes = item.nodes.map((node, idx) => {
        const uniqueId = `g${groupIdx}_${idx}`
        nodeIdMap[`${groupIdx}_${idx}`] = uniqueId
        return {
          ...node,
          id: uniqueId,
          name: node.name ? `g${groupIdx}_${node.name}` : uniqueId,
          symbolSize,
          category: groupIdx,
          itemStyle: {
            color: colorList[groupIdx % colorList.length],
            borderColor: '#fff',
            borderWidth: 2,
            opacity: 1
          }
        }
      })
      const links = item.links.map(link => {
        const getId = v => typeof v === 'number' ? `g${groupIdx}_${v}` : `g${groupIdx}_${v}`
        return {
          ...link,
          source: getId(link.source),
          target: getId(link.target),
          category: groupIdx
        }
      })
      allNodes = allNodes.concat(nodes)
      allLinks = allLinks.concat(links)
      nodeOffset += item.nodes.length
    })
    const option = {
      series: [{
        name: '关系图',
        type: 'graphGL',
        layout: 'forceAtlas2',
        modularity: true,
        data: allNodes,
        links: allLinks,
        categories: categories,
        focusNodeAdjacency: true,
        roam: true,
        label: {
          show: true,
          position: 'right',
          fontSize: 12,
          color: '#fff'
        },
        lineStyle: {
          color: 'source',
          curveness: 0.3,
          opacity: 0.8,
          width: 2
        },
        forceAtlas2: {
          steps: 1,
          stopThreshold: 0.1,
          jitterTolerence: 10,
          edgeWeight: [0.2, 1],
          gravity: 0.1,
          scaling: 1.5,
          preventOverlap: true
        }
      }]
    }
    chart.setOption(option, true)
  }
}

defineExpose({
  exportImg,
  getChartInstance,
  updateData
})
</script>

<template>
  <div ref="chartRef" style="width:100%; height:100%; min-height:600px; overflow:auto"></div>
</template>

<style scoped>
/* WebGL 版本的特殊样式 */
.chart-container {
  position: relative;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 性能优化提示 */
.chart-container::before {
  content: 'WebGL 优化版本';
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(38, 183, 255, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
}
</style>
