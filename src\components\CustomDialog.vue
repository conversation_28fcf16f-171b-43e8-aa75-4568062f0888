<script setup>
import { ref, defineExpose, defineProps, defineEmits, watch } from "vue";


const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: '数据导入'
  },
  showClose: {
    type: Boolean,
    default: false
  },
  fullscreen: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['close', 'update:visible'])
const dialogVisible = ref(props.visible)

watch(() => props.visible, (val) => {
  console.log('watch', val)
  dialogVisible.value = val
})

watch(() => dialogVisible.value, (val) => {
  if (props.visible !== val) {
    emits('update:visible', val)
  }
})

const handleClose = () => {
  dialogVisible.value = false
  emits('close')
}

defineExpose({
  open() {
    dialogVisible.value = true
  }
})
</script>

<template>
  <el-dialog
      v-model="dialogVisible"
      width="1272"
      :show-close="false"
      align-center
      :fullscreen="fullscreen"
      modal-class="custom-dialog"
      :before-close="handleClose"
  >
    <div class="custom-dialog_header">
      <h2 class="custom-dialog_title linear-text">{{ title }}</h2>
      <div v-if="showClose" class="close-icon" @click="handleClose"></div>
    </div>
    <div class="custom-dialog_content flex-column">
      <slot></slot>
    </div>
    <template #footer>

    </template>
  </el-dialog>
</template>

<style lang="scss">
.custom-dialog {
  .el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .el-dialog__header, .el-dialog__footer {
    display: none;
  }
  .el-dialog {
    display: flex;
    height: 731px;
    background: url("@/assets/images/bg_dialog.png") no-repeat center;
    background-size: 100% 100%;
    padding: 0;
    &.is-fullscreen {
      height: 100%;
      display: flex;
      flex-direction: column;
      //background: rgba(0, 0, 0, 0.2);
      //backdrop-filter: blur(10px);
    }
  }
}
.custom-dialog_content {
  flex: 1;
  box-sizing: border-box;
  padding: 10px;
  box-sizing: border-box;
}
.custom-dialog_title {
  font-family: 'ysbth';
  text-align: center;
  font-size: 48px;
  font-weight: 400;
  height: 9.5vh;
  line-height: 7vh;
}
.custom-dialog_header {
  position: relative;
}
.close-icon {
  width: 50px;
  height: 50px;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: url("@/assets/images/close.png") no-repeat center;
  background-size: 35px;
  cursor: pointer;
}

/* 非全屏模式下的样式调整 */
.custom-dialog .el-dialog:not(.is-fullscreen) {
  .custom-dialog_title {
    padding-top: 10px; /* 只在非全屏模式下添加上边距 */
    height: 6.5vh;
    line-height: 4vh;
  }
  
  .close-icon {
    top: 45%; /* 只在非全屏模式下调整关闭按钮位置 */
  }
  .custom-dialog_content {
    padding: 5px 10px;
  }
}
</style>