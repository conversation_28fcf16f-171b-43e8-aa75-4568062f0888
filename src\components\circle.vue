
<script setup>
import {onMounted, ref, defineProps, watch, defineEmits, computed} from "vue";

const props = defineProps({
  progress: {
    type: Number,
    default: 0
  },
  done: {
    type: Boolean,
    default: false
  },
  curStep: {
    type: Object,
    default: () => ({})
  }
})
const emits = defineEmits(['finished'])

const circleRef = ref()

const color = '#fff' // #66a6ff  #def4ff
const shadowColor = '#fff' // #66a6ff  #def4ff
let timer = null

const progressMap = {
  0: {
    mapping: 5,
    name: '上传文件中'
  },
  5: {
    mapping: 10,
    name: 'MD5校验中'
  },
  10: {
    mapping: 15,
    name: '解压文件中'
  },
  15: {
    mapping: 20,
    name: '分析数据中'
  },
  20: {
    mapping: 88,
    name: '清洗数据中'
  },
  88: {
    mapping: 100,
    name: '生成数据中',
    pause: true // 等到这一步就暂停，等待 done 触发
  }
}

const curProgress = ref({})

let ctx = null;
const totalTicks = 140;
const tickLength = 35;
const tickWidth = 8;
const startAngle = -Math.PI/2;
let percent = ref(0);

// 呼吸效果参数调整
let startTime = null;
const breathDuration = 1000; // 1秒完整呼吸周期
let cx,cy,radius,W,H;

onMounted(() => {
  init()
})
const startProgress = (timeout = 1500) => {
  // 动态动画演示
  let cur = percent.value;
  clearInterval(timer);
  timer = setInterval(() => {
    if (cur >= curProgress.value.mapping) {
      if (props.done) {
        console.log('完成了✅✅✅✅');
        setTimeout(() => {
          emits('finished')
        }, 2000)
      }
      clearInterval(timer);
      timer = null
    } else {
      cur += 1; // 每次增加1%
    }
    percent.value = cur
    drawProgress(cur, performance.now());
  }, timeout);
}
// 监听 progress 变化
watch([() => props.progress, () => props.done, () => percent.value], ([newP, newD, newPercent], [oldP, oldD, oldPercent]) => {
  // console.log('watch triggered:', newP, newD, newPercent);
  // 只有当外部 progress 更新 或者 本地 percent 刚好达到上一个 step 临界值时，重启动画
   if ((newP !== oldP && typeof newP === 'number') || (progressMap[oldPercent] && newPercent === progressMap[oldPercent].mapping)) {
      console.log('开始进度动画', newP, newD, newPercent);
      startProgress()
   }
  // 算出当前 curProgress
  if (oldPercent !== newPercent && progressMap[newPercent] && !progressMap[newPercent].pause) {
    curProgress.value = progressMap[newPercent]
  }

  if (props.done) {
    curProgress.value = Object.values(progressMap)[Object.values(progressMap).length - 1]
    startProgress(500)
  }
}, { immediate: true})

const init = () => {
  const canvas = circleRef.value;
  ctx = canvas.getContext('2d');
  W = canvas.width
  H = canvas.height;
  cx = W/2
  cy = H/2;
  radius = Math.min(W, H) * 0.37; // 动态计算半径
  drawProgress(percent.value)

  // requestAnimationFrame((ts) => drawProgress(percent, ts));
  startProgress()
}



const drawProgress = (p, timestamp) => {
  if (!startTime) startTime = timestamp;
  const elapsed = timestamp - startTime;
  const breathFactor = Math.sin((elapsed / breathDuration) * Math.PI * 2) * 0.5 + 0.5; // 0-1之间变化

  ctx.clearRect(0,0,W,H);

  // 计算当前呼吸状态下的阴影值
  const currentTickShadow = 20 + 40 * breathFactor;
  const currentOuterShadow = 20 + 40 * breathFactor;
  const currentTextShadow = 15 + 40 * breathFactor;

  // 1. 背景刻度
  ctx.shadowBlur = 0;
  for (let i = 0; i < totalTicks; i++) {
    const ang = startAngle + (i/totalTicks)*Math.PI*2;
    const x1 = cx + (radius - tickLength)*Math.cos(ang);
    const y1 = cy + (radius - tickLength)*Math.sin(ang);
    const x2 = cx + radius * Math.cos(ang);
    const y2 = cy + radius * Math.sin(ang);

    ctx.beginPath();
    ctx.lineWidth = tickWidth;
    ctx.strokeStyle = 'transparent';
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
  }

  // 2. 高亮刻度
  const litCount = Math.floor(totalTicks * p / 100);
  for (let i = 0; i < litCount; i++) {
    const ang = startAngle + (i/totalTicks)*Math.PI*2;
    const x1 = cx + (radius - tickLength)*Math.cos(ang);
    const y1 = cy + (radius - tickLength)*Math.sin(ang);
    const x2 = cx + radius * Math.cos(ang);
    const y2 = cy + radius * Math.sin(ang);

    ctx.save();
    ctx.beginPath();
    ctx.lineWidth = tickWidth;
    ctx.strokeStyle = color;
    ctx.shadowBlur = currentTickShadow;
    ctx.shadowColor = shadowColor;
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
    ctx.restore();
  }

  // 3. 外圈发光环
  ctx.save();
  ctx.beginPath();
  ctx.arc(cx, cy, radius + 30, 0, Math.PI*2);
  ctx.lineWidth = 35;
  ctx.strokeStyle = color;
  ctx.shadowBlur = currentOuterShadow;
  ctx.shadowColor = shadowColor;
  ctx.stroke();
  ctx.restore();

  const isDone = props.done && p === 100;
  // 计算字体大小
  const mainFontSize = isDone ? 75 : 120;
  const subFontSize = 28;
  const mainText = isDone ? 'COMPLETE' : `${p}%`;
  const subText = curProgress.value.name || 'Processing...';

  // 计算两行文字总高度
  const totalTextHeight = mainFontSize + subFontSize + 20; // 20为行间距
  const baseY = cy - totalTextHeight / 2;

  // 主文字
  ctx.save();
  ctx.fillStyle = color;
  ctx.font = `400 ${mainFontSize}px ysbth`;
  ctx.textAlign = 'center';
  ctx.textBaseline = isDone ? 'middle' : 'top';
  ctx.shadowBlur = currentTextShadow;
  ctx.shadowColor = color;
  ctx.fillText(mainText, cx, isDone ? cy : baseY);
  ctx.restore();

  if (!isDone) {
    // 小字
    ctx.save();
    ctx.fillStyle = '#fff';
    ctx.font = `400 ${subFontSize}px ysbth`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    ctx.shadowBlur = 0;
    ctx.fillText(subText, cx, baseY + mainFontSize + 20);
    ctx.restore();
  }

  requestAnimationFrame((ts) => drawProgress(p, ts));
}

</script>


<template>
  <canvas ref="circleRef" class="circle" width="800" height="800"></canvas>
</template>

<style scoped lang="less">

</style>

