<script setup>
import CustomDialog from "@/components/CustomDialog.vue";
import CustomButton from "@/components/CustomButton.vue";
import { ref, useTemplateRef, defineExpose, onUnmounted } from "vue";
import { useUploadStore } from "@/stores/index.js";
import { ElMessage } from 'element-plus'
import { startClean, getProgress, downloadTemplate } from "@/api/index.js";
import { useRouter } from "vue-router";
import { showLoading, hideLoading } from "@/utils/loading.js";
import { downloadBlob } from "@/utils/index.js";

const router = useRouter();
const api = import.meta.env.VITE_APP_BASE_API || ''
const uploadStore = useUploadStore();

const dialogRef = useTemplateRef('dialog')
const showProgress = ref(false);
const fileList = ref([]); // 示例文件列表
const percentage = ref(0);
const accept = 'xlsx'; // 支持的文件格式
const acceptList = accept.split(',').map(type => type.trim()).map(type => `.${type}`).join(',');
const onBeforeUpload = file => {
  console.log(file)
  /*if (file.size > 1024 * 1024 * 1024) { // 限制文件大小为1GB
    ElMessage.error('文件大小不能超过1GB');
    return false;
  }*/
  // 检查文件类型
  const fileExtension = file.name.split('.').pop().toLowerCase();
  console.log('文件后缀名:', fileExtension, accept.indexOf(fileExtension));
  // 2检查后缀名是否在允许的列表中
  if (accept.indexOf(fileExtension) < 0) {
    ElMessage.error(`不支持的文件类型: ${fileExtension}`);
    return false;
  }
  showProgress.value = true
  return true
}
const onProgress = (event) => {
  console.log('上传进度:', event);
  percentage.value = parseInt(event.percent);
};
const onSuccess = (res) => {
  showProgress.value = false;
  if (res.code === 0) {
    // 上传成功后可以在这里处理逻辑
    fileList.value = res.data.uploaded_files
    console.log('上传成功', res);
    uploadStore.setSessionId(res.session_id);
  } else {
    // 上传失败处理
    // console.error('上传失败', res.message);
  }
}

// 进度轮询定时器
let progressTimer = null;

// 递归获取进度
const pollProgress = async () => {
  try {
    const { code, data } = await getProgress({ session_id: uploadStore.sessionId });
    
    if (code === 0) {
      console.log('当前进度:', data);
      
      // 根据进度状态处理
      if (data.state === 'done') {
        // 清洗完成
        clearInterval(progressTimer);
        hideLoading();
        // ElMessage.success('清洗任务已完成');
        await router.push({name: 'home', query: {result: 1, session_id: uploadStore.sessionId}}); // 跳转到结果页面
        setTimeout(() => {
          location.reload();
        }, 100)
      } else if (data.state === 'failed') {
        // 清洗失败
        clearInterval(progressTimer);
        hideLoading();
        ElMessage.error('清洗任务失败');
      } else if (data.state === 'processing') {
        // 清洗进行中，继续轮询
        console.log('清洗进度:', data.progress || 0);
      }
    } else {
      console.error('获取进度失败:', data);
    }
  } catch (error) {
    console.error('获取进度出错:', error);
    // 出错时不停止轮询，继续尝试
  }
};

const onStart = async () => {
  // 显示自定义loading
  showLoading('研判中...');
  
  try {
    const { code } = await startClean({ session_id: uploadStore.sessionId });
    if (code === 0) {
      console.log('清洗任务已开始');
      // 开始轮询进度，每5秒一次
      progressTimer = setInterval(pollProgress, 5000);
      
      // 立即执行一次获取进度
      await pollProgress();
      
    } else {
      ElMessage.error('清洗任务启动失败，请稍后再试');
      hideLoading();
    }
  } catch (error) {
    ElMessage.error('清洗任务启动失败，请稍后再试');
    hideLoading();
  }
}

const open = () => {
  dialogRef.value.open();
};

// 组件卸载时清理定时器
const cleanup = () => {
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }
};
const onDownloadTemplate = async () => {
  const data = await downloadTemplate();
  downloadBlob(data, 'xlsx', '模板');
}

// 暴露清理方法
defineExpose({
  open,
  cleanup
});

// 组件卸载时自动清理
onUnmounted(() => {
  cleanup();
});
</script>

<template>
  <custom-dialog :visible="false" fullscreen ref="dialog">
    <div class="import-content flex-column">
      <div class="download-template-box">
        <el-link class="download-template-link" type="primary" @click="onDownloadTemplate">下载模板</el-link>
      </div>
      <el-upload class="upload-demo" drag :action="`${api}/api/v1/file/file_upload`" :accept="acceptList"
        :show-file-list="false" name="files" :data="{
            session_id: uploadStore.sessionId
          }" :before-upload="onBeforeUpload" :on-progress="onProgress" :on-success="onSuccess">
        <el-icon class="el-icon--upload">
          <img src="@/assets/images/icon_upload.png" />
        </el-icon>
        <div class="el-upload__text">
          <h5>拖拽文件到此处或点击上传</h5>
          <p>支持 {{ accept.replaceAll(',', '、') }} 格式文件</p>
        </div>
      </el-upload>

      <div class="files-content">
        <template v-if="showProgress">
          <el-progress class="custom-progress" :percentage="percentage" :show-text="false" :stroke-width="14" />
          <p class="upload-progress-text">上传中 整体进度：{{ percentage }}%</p>
        </template>
        <el-scrollbar v-show="!showProgress" style="height: 100%">
          <ul class="file-list">
            <li v-for="item in fileList">{{ item }}</li>
          </ul>
        </el-scrollbar>
      </div>
      <div class="start-box">
        <CustomButton :disabled="showProgress || !fileList.length" @click="onStart">开始研判</CustomButton>
      </div>
    </div>
  </custom-dialog>
</template>

<style lang="scss" scoped>
.import-content {
  padding: 10vh 0 0;
  width: 70%;
  margin: 0 auto;
  flex: 1;
  :deep(.el-upload-dragger) {
    background: linear-gradient(90deg, rgba(3, 58, 88, 0.9) 0%, rgba(5, 71, 115, 0.9) 50%, rgba(3, 58, 88, 0.9) 100%);
    padding: 10vh 10px;
    //height: 220px;
    border: 2px dashed #226CBE;
    border-radius: 0;
    transition: all .5s;
    &.is-dragover {
      border-color: #CCE5FF;
    }
  }
  .el-upload__text {
    line-height: 1;
    h5 {
      font-weight: 400;
      font-size: 16px;
      color: #CCE5FF;
    }
    p {
      padding-top: 17px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(204, 229, 255, 0.6);
    }
  }
  .files-content {
    height: 100px;
    .file-list {
      list-style: none;
      margin: 0;
      padding: 5px;
      box-sizing: border-box;
      li {
        margin: 0;
        color: rgba(204, 229, 255, 0.6);
        padding: 5px 0;
      }
    }
  }
  .custom-progress {
    padding-top: 52px;
  }
  :deep(.el-progress) {
    .el-progress-bar__outer {
      background: rgba(24,152,251,0.2);
      border-radius: 0;
    }
    .el-progress-bar__inner {
      border-radius: 0;
      background: linear-gradient(to left, #1685DE , rgba(22, 133, 222, 0.2));
      border: 2px solid;
      border-image-source: linear-gradient(to right, #046BA3 , #00C0FF);
      border-image-slice: 1;
      box-sizing: border-box;
    }
  }
  .upload-progress-text {
    font-size: 14px;
    color: #CCE5FF;
    opacity: 0.6;
    padding-top: 15px;
  }
  .start-box {
    flex: 1;
    display: flex;
    align-items: flex-end;
    padding-bottom: 55px;
    justify-content: center;
  }
  .download-template-box {
    display: flex;
    justify-content: flex-end;
  }
  .download-template-link {
    font-size: 14px;
    padding-top: 15px;
    margin-bottom: 10px;
    font-size: 20px;
    color: #CCE5FF;
  }
}
</style>