<script setup>
import { defineProps, defineEmits } from 'vue';
const props = defineProps({
  // 可以在这里定义组件的props
  disabled: {
    type: Boolean,
    default: false
  }
});
const emits = defineEmits(['click']);
</script>

<template>
  <button class="custom_btn" :disabled="disabled" @click="emits('click')"><span class="linear-text"><slot>开始清洗</slot></span></button>
</template>

<style scoped lang="scss">
.custom_btn {
  width: 262px;
  height: 83px;
  background: url("@/assets/images/start_btn.png") no-repeat center;
  background-size: 100% 100%;
  font-family: 'ysbth';
  font-size: 42px;
  outline: none;
  border: none;
  cursor: pointer;
  &[disabled] {
    cursor: not-allowed;
    opacity: .7;
  }
}
</style>