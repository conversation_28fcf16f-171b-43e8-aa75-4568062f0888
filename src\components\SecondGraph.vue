<script setup>
import CustomDialog from "@/components/CustomDialog.vue";
import Graph from '@/components/graph.vue'
import { useTemplateRef, defineExpose, ref, defineModel, defineEmits } from "vue";

const dialogRef = useTemplateRef('dialog')
const graphRef = useTemplateRef('graph')
const graphData = ref([])
const visible = ref(false)
const curMainNode = ref(null)
const maxDepth = defineModel('maxDepth', {
  type: Number,
  default: 2
})
const emit = defineEmits(['depthChange'])

const updateData = (data) => {
  graphData.value = [data]
  if (graphRef.value) {
    graphRef.value.updateData([data])
  }
}

const onNodeClick = (data) => {
  console.log('节点点击:', data)
}

const onDepthChange = (val) => {
  emit('depthChange', {
    node: curMainNode.value,
    depth: val
  })
}

const open = (node) => {
  visible.value = true
  curMainNode.value = node
};
const onClose = () => {
  maxDepth.value = 2
  console.log('onclose')
  graphData.value = []
  visible.value = false
}

defineExpose({
  open,
  updateData
});
</script>

<template>
  <custom-dialog v-model:visible="visible" ref="dialog" :fullscreen="false" show-close :title="'节点关系图'"
    @close="onClose">
    <div class="second-graph-content">
      <div class="relation-count-select">
        <span>层级深度</span>
        <el-select v-model="maxDepth" placeholder="关系线数量" @change="onDepthChange">
          <el-option v-for="item in 15" :key="item" :label="item" :value="item" />
        </el-select>
      </div>
      <graph v-if="graphData.length > 0" ref="graph" :series="graphData" @node-click="onNodeClick" />
      <div v-else class="no-data">暂无数据</div>
    </div>
  </custom-dialog>
</template>

<style lang="scss" scoped>
.second-graph-content {
  width: 100%;
  height: 100%;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .relation-count-select {
      width: 200px;
      position: absolute;
      left: 10px;
      top: 10px;
      z-index: 999;
      display: flex;
        align-items: center;
        gap: 5px;
      > span {
          color: #fff;
          white-space: nowrap;
        }
      :deep(.el-select__placeholder) {
        color: #fff !important;
      }
  
      :deep(.el-select__wrapper) {
        background-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        --el-input-border-color: #02AAE5;
        box-shadow: none;
        border: 1px solid #02AAE5;
      }
    }

  .no-data {
    color: #fff;
    font-size: 18px;
  }
}
</style>