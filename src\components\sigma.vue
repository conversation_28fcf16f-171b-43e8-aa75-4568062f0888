<script setup>
import { ref, onMounted, nextTick, defineProps, defineExpose, computed } from 'vue'
import { Graph } from 'graphology'
import Sigma from 'sigma'

const props = defineProps({
  series: {
    type: Array,
    default: () => []
  }
})

const chartRef = ref(null)
const sigmaInstance = ref(null)
const graph = ref(null)

// 颜色列表，与echarts保持一致
const colorList = ['#26b7ff', '#21bcdb', '#2e99e5', '#2e5ce5', '#5c67e5', '#39bfb4', '#6159b2', '#7a5c99', '#995c8f']

// 合并所有分组为一个graphology图，节点用category区分
const mergedGraphData = computed(() => {
  let allNodes = []
  let allLinks = []
  let categories = []
  let nodeIdMap = {} // 用于映射原始索引到唯一id
  props.series.forEach((item, groupIdx) => {
    categories.push({ name: `团伙${groupIdx+1}` })
    const nodes = item.nodes.map((node, idx) => {
      const uniqueId = node.id || `g${groupIdx}_${idx}`
      nodeIdMap[`${groupIdx}_${idx}`] = uniqueId
      // 自动分组布局：每组一个圆圈
      const groupCount = props.series.length;
      const groupRadius = 5 + item.nodes.length * 2; // 每组半径
      const groupAngle = (2 * Math.PI * groupIdx) / groupCount;
      const centerX = Math.cos(groupAngle) * 30;
      const centerY = Math.sin(groupAngle) * 30;
      const nodeAngle = (2 * Math.PI * idx) / item.nodes.length;
      // 强制转为数字，防止意外类型
      const x = Number(centerX + Math.cos(nodeAngle) * groupRadius);
      const y = Number(centerY + Math.sin(nodeAngle) * groupRadius);
      if (isNaN(x) || isNaN(y)) {
        console.warn('节点坐标无效', { groupIdx, idx, node, x, y });
      }
      return {
        ...node,
        id: uniqueId,
        name: node.name || uniqueId,
        category: groupIdx,
        color: colorList[groupIdx % colorList.length],
        size: 20 + Math.min(30, 50 - item.nodes.length),
        x,
        y
      }
    })
    // links的source/target要用唯一id
    const getId = v => {
      if (typeof v === 'number') {
        return nodeIdMap[`${groupIdx}_${v}`];
      } else {
        // 先找id，再找name
        const idx = item.nodes.findIndex(n => n.id === v || n.name === v);
        if (idx !== -1) {
          return nodeIdMap[`${groupIdx}_${idx}`];
        }
        // 兜底：如果v本身就是id
        if (Object.values(nodeIdMap).includes(v)) {
          return v;
        }
        // 没找到，返回undefined
        return undefined;
      }
    };
    const links = item.links.map(link => {
      const sourceId = getId(link.source);
      const targetId = getId(link.target);
      if (!sourceId || !targetId) {
        // 调试输出
        console.warn('边节点未找到', { groupIdx, link, sourceId, targetId, nodeIdMap, nodes: item.nodes });
      }
      return {
        ...link,
        source: sourceId,
        target: targetId,
        category: groupIdx
      }
    }).filter(link => link.source && link.target); // 只保留合法边
    allNodes = allNodes.concat(nodes)
    allLinks = allLinks.concat(links)
  })
  return {
    nodes: allNodes,
    links: allLinks,
    categories
  }
})

// 初始化sigma图谱
const renderSigma = () => {
  if (!chartRef.value) return
  // 清理旧实例
  if (sigmaInstance.value) {
    sigmaInstance.value.kill()
    sigmaInstance.value = null
  }
  // 构建graphology图
  const g = new Graph()
  mergedGraphData.value.nodes.forEach(node => {
    g.addNode(node.id, {
      label: node.name,
      color: node.color,
      size: node.size,
      category: node.category
    })
  })
  mergedGraphData.value.links.forEach((edge, i) => {
    // 避免重复边id
    g.addEdgeWithKey(`e${i}`, edge.source, edge.target, {
      color: colorList[edge.category % colorList.length],
      category: edge.category
    })
  })
  graph.value = g
  // 创建sigma实例
  sigmaInstance.value = new Sigma(g, chartRef.value, {
    renderLabels: true,
    labelColor: {
      color: '#fff',
      mode: 'default'
    },
    defaultNodeColor: '#26b7ff',
    defaultEdgeColor: '#aaa',
    zIndex: true
  })
  // 自适应大小
  setTimeout(() => {
    sigmaInstance.value.refresh()
  }, 100)
}

onMounted(() => {
  nextTick(() => {
    renderSigma()
    window.addEventListener('resize', () => {
      sigmaInstance.value && sigmaInstance.value.refresh()
    })
  })
})

defineExpose({
  // 暴露导出图片方法（可选，需canvas截图实现）
})
</script>

<template>
  <div ref="chartRef" style="width:100%; height:100%; min-height:600px; overflow:auto"></div>
</template>
