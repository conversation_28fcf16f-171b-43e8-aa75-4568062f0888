<script setup>
import { onMounted, ref, reactive } from "vue";
import Circle from "@/components/circle.vue";
import { getProgress } from "@/api/index.js";
import { useUploadStore } from "@/stores/index.js";
import { useRouter } from "vue-router";

const router = useRouter();
const uploadStore = useUploadStore();

const progress = ref(0); // 进度百分比
const timer = ref(null); // 定时器
const isDone = ref(false); // 是否完成
const curStep = ref({})
const result = reactive({
  total_data_count: 0,
  total_file_size: 0,
})
onMounted(() => {
  updateProgress();
})

const updateProgress = async () => {
  try {
    const { code, data } = await getProgress({ session_id: uploadStore.sessionId });
    if (code === 0) {
      console.log('获取进度数据成功', data.percent, data.state);
      progress.value = data.percent;
      curStep.value = data.steps[data.steps.length - 1] || {}
      if (data.state === 'done') {
        isDone.value = true;
        result.total_data_count = data.total_data_count
        result.total_file_size = data.total_file_size;
        clearTimeout(timer.value);
        timer.value = null
      }

    } else {
      console.error('获取进度数据失败', data);
    }
  } finally {
    timer.value = setTimeout(updateProgress, 5000); // 每5秒更新一次进度
  }
}

const onFinished = () => {
  clearTimeout(timer.value);
  router.push({ name: 'home', query: { result: 1, session_id: uploadStore.sessionId, ...result } }); // 跳转到结果页面
}

</script>

<template>
  <div class="progress-view">
    <video class="progress-video" src="@/assets/videos/progress.mp4" autoplay muted loop preload="auto" />
    <Circle class="circle-progress" :progress="progress" :cur-step="curStep" :done="isDone" @finished="onFinished" />
  </div>
</template>

<style scoped lang="scss">
  .progress-view {
    box-sizing: border-box;
    flex: 1;
    overflow: hidden;
    background: #000;
    .progress-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      flex: 1;
    }
    .circle-progress {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      //width: 600px;
      //height: 600px;
    }
  }
</style>