
import axios from 'axios'

// 创建axios实例
const service = axios.create({
    baseURL: import.meta.env.VITE_APP_BASE_API,
    timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        // 在发送请求前做些什么（如添加token）
        const token = localStorage.getItem('token')
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`
        }
        return config
    },
    error => {
        // 对请求错误做些什么
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        // console.log(response.request.responseType)
        // 对响应数据做点什么
        const res = response.data
        // console.log(res)
        if (res.code !== 0 && response.request.responseType !== 'blob') {
            // 处理业务错误
            console.error('请求错误:', res.message)
            return Promise.reject(new Error(res.message || 'Error'))
        } else {
            return res
        }
    },
    error => {
        // 对响应错误做点什么
        console.error('请求失败:', error.message)
        return Promise.reject(error)
    }
)

export default service
