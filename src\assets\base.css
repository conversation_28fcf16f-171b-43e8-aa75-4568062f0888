html, body, #app {
    height: 100%;
    margin: 0;
    padding: 0;
}
h1, h2, h3, h4, h5, h6, p {
    margin: 0;
    padding: 0;
}

.flex-column {
    display: flex;
    flex-direction: column;
}
/*自定义一个字体叫ysbth*/
@font-face {
    font-family: 'ysbth';
    src: url('@/assets/fonts/ysbth.ttf') format('truetype');
}

.linear-text {
    font-family: 'ysbth';
    background: linear-gradient(to bottom, #FFF, #CCEAFF);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    filter: drop-shadow(0 2px 2px rgba(0,0,0,0.5));
}
