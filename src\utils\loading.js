import { createApp, h } from 'vue'
import CustomLoading from '@/components/CustomLoading.vue'

let loadingInstance = null

// 创建loading实例
const createLoadingInstance = () => {
  const container = document.createElement('div')
  document.body.appendChild(container)
  
  const app = createApp({
    render() {
      return h(CustomLoading, {
        ref: 'loadingRef'
      })
    }
  })
  
  const instance = app.mount(container)
  
  return {
    instance: instance.$refs.loadingRef,
    container,
    app
  }
}

// 显示loading
export const showLoading = (text = '请稍候...') => {
  if (!loadingInstance) {
    loadingInstance = createLoadingInstance()
  }
  loadingInstance.instance.show(text)
}

// 隐藏loading
export const hideLoading = () => {
  if (loadingInstance) {
    // 添加小延迟确保动画效果完整
    setTimeout(() => {
      if (loadingInstance) {
        loadingInstance.instance.hide()
      }
    }, 100)
  }
}

// 带Promise的loading包装器
export const withLoading = async (promise, text = '请稍候...') => {
  showLoading(text)
  try {
    const result = await promise
    return result
  } finally {
    hideLoading()
  }
}

// 销毁loading实例
export const destroyLoading = () => {
  if (loadingInstance) {
    loadingInstance.app.unmount()
    document.body.removeChild(loadingInstance.container)
    loadingInstance = null
  }
} 